#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主脚本：运行所有三个脚本
"""

import os
import sys
import subprocess

def run_script(script_name, description):
    """
    运行指定的脚本
    
    Args:
        script_name (str): 脚本文件名
        description (str): 脚本描述
    """
    print(f"\n{'='*60}")
    print(f"正在运行：{description}")
    print(f"脚本文件：{script_name}")
    print(f"{'='*60}")
    
    try:
        # 运行脚本
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              encoding='utf-8')
        
        # 输出结果
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误输出：")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 执行成功")
            return True
        else:
            print(f"❌ {description} 执行失败，返回码：{result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 运行脚本时出现异常：{e}")
        return False

def main():
    print("🚀 开始执行所有脚本")
    print("=" * 60)
    
    # 脚本列表
    scripts = [
        ("script1_renumber_questions.py", "脚本1：重新编号 example_questions"),
        ("script2_convert_and_shuffle.py", "脚本2：转换格式并打乱"),
        ("script3_merge_files.py", "脚本3：验证子集关系并合并文件")
    ]
    
    success_count = 0
    
    # 依次运行每个脚本
    for script_name, description in scripts:
        if not os.path.exists(script_name):
            print(f"❌ 脚本文件 {script_name} 不存在")
            continue
        
        success = run_script(script_name, description)
        if success:
            success_count += 1
        else:
            print(f"\n⚠️  脚本 {script_name} 执行失败，是否继续执行后续脚本？")
            user_input = input("输入 'y' 继续，其他任意键退出：").strip().lower()
            if user_input != 'y':
                break
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 执行总结")
    print(f"{'='*60}")
    print(f"总脚本数：{len(scripts)}")
    print(f"成功执行：{success_count}")
    print(f"执行失败：{len(scripts) - success_count}")
    
    if success_count == len(scripts):
        print("\n🎉 所有脚本执行成功！")
    else:
        print(f"\n⚠️  有 {len(scripts) - success_count} 个脚本执行失败")
    
    print("\n生成的文件：")
    files_to_check = [
        r"D:\python_project\dify\processed_labels_output.json",
        r"D:\python_project\dify\merge.json", 
        r"D:\python_project\dify\merged_result.json"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"  ✅ {file_path} ({size:,} 字节)")
        else:
            print(f"  ❌ {file_path} (不存在)")

if __name__ == "__main__":
    main()
