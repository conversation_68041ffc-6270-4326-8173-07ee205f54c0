#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本1：重新编号 processed_labels_output.json 文件中每个 example_questions 字段的内容
"""

import json
import re
import os

def renumber_example_questions(input_file, output_file=None):
    """
    重新编号 example_questions 字段中的内容
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径，如果为None则覆盖原文件
    """
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return False
    
    try:
        # 读取JSON文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"开始处理文件：{input_file}")
        print(f"共有 {len(data)} 个标签条目")
        
        # 处理每个标签条目
        for i, item in enumerate(data):
            if 'example_questions' in item and isinstance(item['example_questions'], list):
                # 重新编号每个问题
                for j, question in enumerate(item['example_questions']):
                    # 使用正则表达式匹配开头的数字编号
                    # 匹配模式：开头的数字、中文数字、或者"第X题"等格式
                    pattern = r'^(\d+[、．.]|第\d+[题道]|[一二三四五六七八九十]+[、．.])'
                    
                    # 移除原有编号
                    cleaned_question = re.sub(pattern, '', question).strip()
                    
                    # 添加新编号
                    item['example_questions'][j] = f"{j+1}、{cleaned_question}"
                
                print(f"处理完成第 {i+1} 个标签条目，共 {len(item['example_questions'])} 个问题")
        
        # 保存结果
        output_path = output_file if output_file else input_file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"重新编号完成，结果已保存到：{output_path}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
        return False
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        return False

def main():
    input_file = r"D:\python_project\dify\processed_labels_output.json"
    
    print("=" * 50)
    print("脚本1：重新编号 example_questions 字段内容")
    print("=" * 50)
    
    # 执行重新编号
    success = renumber_example_questions(input_file)
    
    if success:
        print("\n✅ 重新编号完成！")
    else:
        print("\n❌ 重新编号失败！")

if __name__ == "__main__":
    main()
