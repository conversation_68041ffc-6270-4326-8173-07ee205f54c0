#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本3：将 processed_labels_output.json 转换为 merge.json 格式并打乱
"""

import json
import random
import os
from collections import defaultdict

def convert_to_merge_format(input_file, output_file):
    """
    将 processed_labels_output.json 转换为 merge.json 格式
    
    Args:
        input_file (str): 输入文件路径
        output_file (str): 输出文件路径
    """
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return False
    
    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"开始转换文件：{input_file}")
        print(f"共有 {len(data)} 个标签条目")
        
        # 转换为新格式
        converted_data = []
        total_questions = 0
        
        for item in data:
            if 'label_path' in item and 'example_questions' in item:
                # 将 label_path 转换为 doc_label 列表
                doc_label = item['label_path'].split(' -> ')
                
                # 为每个问题创建一个条目
                for question in item['example_questions']:
                    converted_item = {
                        "doc_token": question,
                        "doc_label": doc_label
                    }
                    converted_data.append(converted_item)
                    total_questions += 1
        
        print(f"转换完成，共生成 {total_questions} 个问题条目")
        
        # 打乱数据，避免连续出现同一标签
        print("开始打乱数据...")
        shuffled_data = smart_shuffle(converted_data)
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(shuffled_data, f, ensure_ascii=False, indent=2)
        
        print(f"转换和打乱完成，结果已保存到：{output_file}")
        return True
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
        return False
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        return False

def smart_shuffle(data):
    """
    智能打乱数据，尽量避免连续出现相同标签
    
    Args:
        data (list): 要打乱的数据列表
        
    Returns:
        list: 打乱后的数据列表
    """
    # 按标签路径分组
    label_groups = defaultdict(list)
    for item in data:
        label_key = " -> ".join(item['doc_label'])
        label_groups[label_key].append(item)
    
    print(f"共有 {len(label_groups)} 个不同的标签")
    
    # 打乱每个组内的数据
    for label_key in label_groups:
        random.shuffle(label_groups[label_key])
    
    # 使用轮询方式分配数据，避免连续相同标签
    result = []
    label_keys = list(label_groups.keys())
    random.shuffle(label_keys)  # 随机化标签顺序
    
    # 计算每个标签的数据量
    max_items = max(len(items) for items in label_groups.values())
    
    # 轮询分配
    for round_num in range(max_items):
        # 随机化本轮的标签顺序
        current_round_labels = label_keys.copy()
        random.shuffle(current_round_labels)
        
        for label_key in current_round_labels:
            if round_num < len(label_groups[label_key]):
                result.append(label_groups[label_key][round_num])
    
    print(f"智能打乱完成，共 {len(result)} 个条目")
    return result

def main():
    input_file = r"D:\python_project\dify\merged_result.json"
    output_file = r"D:\python_project\dify\ultimate_merge_small_labels_numbered_supply.json"
    
    print("=" * 50)
    print("脚本2：转换格式并智能打乱")
    print("=" * 50)
    
    # 设置随机种子以便复现结果
    random.seed(42)
    
    # 执行转换和打乱
    success = convert_to_merge_format(input_file, output_file)
    
    if success:
        print("\n✅ 转换和打乱完成！")
    else:
        print("\n❌ 转换和打乱失败！")

if __name__ == "__main__":
    main()
