#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本4：按标签归组并划分训练集和测试集
"""

import json
import random
import os
from collections import defaultdict

def group_by_label(data):
    """
    按标签归组数据
    
    Args:
        data (list): 原始数据列表
        
    Returns:
        dict: 按标签分组的数据字典
    """
    label_groups = defaultdict(list)
    
    for item in data:
        if 'doc_label' in item:
            # 将 doc_label 列表转换为字符串作为键
            label_key = " -> ".join(item['doc_label'])
            label_groups[label_key].append(item)
    
    return dict(label_groups)

def split_train_test(items, train_ratio=0.9):
    """
    按照指定比例划分训练集和测试集
    
    Args:
        items (list): 同一标签下的所有数据项
        train_ratio (float): 训练集比例，默认0.9
        
    Returns:
        tuple: (训练集, 测试集)
    """
    total_count = len(items)
    
    # 根据题目数量决定划分策略
    if total_count <= 3:
        # 3个及以下全部分给训练集
        return items, []
    elif total_count <= 5:
        # 4-5个题目，训练集取大部分，测试集至少1个
        train_count = total_count - 1
        test_count = 1
    else:
        # 6个及以上按9:1划分，但确保测试集至少1个
        train_count = max(1, int(total_count * train_ratio))
        test_count = total_count - train_count
        
        # 确保测试集至少有1个
        if test_count == 0:
            train_count -= 1
            test_count = 1
    
    # 随机打乱数据
    shuffled_items = items.copy()
    random.shuffle(shuffled_items)
    
    # 划分训练集和测试集
    train_set = shuffled_items[:train_count]
    test_set = shuffled_items[train_count:train_count + test_count]
    
    return train_set, test_set

def process_train_test_split(input_file, train_output_file, test_output_file):
    """
    处理训练集和测试集划分
    
    Args:
        input_file (str): 输入文件路径
        train_output_file (str): 训练集输出文件路径
        test_output_file (str): 测试集输出文件路径
    """
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return False
    
    try:
        # 读取输入文件
        print(f"读取文件：{input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"总数据条目：{len(data):,}")
        
        # 按标签归组
        print("按标签归组...")
        label_groups = group_by_label(data)
        print(f"共有 {len(label_groups)} 个不同的标签")
        
        # 统计信息
        train_data = []
        test_data = []
        split_stats = {
            'total_labels': len(label_groups),
            'train_only_labels': 0,  # 只有训练集的标签数
            'train_test_labels': 0,  # 既有训练集又有测试集的标签数
            'total_train_items': 0,
            'total_test_items': 0,
            'distribution': defaultdict(int)  # 按题目数量分布统计
        }
        
        # 处理每个标签组
        print("\n开始划分训练集和测试集...")
        for label_key, items in label_groups.items():
            item_count = len(items)
            split_stats['distribution'][item_count] += 1
            
            # 划分训练集和测试集
            train_set, test_set = split_train_test(items)
            
            # 添加到总的训练集和测试集
            train_data.extend(train_set)
            test_data.extend(test_set)
            
            # 更新统计信息
            split_stats['total_train_items'] += len(train_set)
            split_stats['total_test_items'] += len(test_set)
            
            if len(test_set) == 0:
                split_stats['train_only_labels'] += 1
            else:
                split_stats['train_test_labels'] += 1
            
            # 显示处理进度（每100个标签显示一次）
            if len(train_data) % 1000 == 0:
                print(f"  已处理 {len([k for k in label_groups.keys() if len(label_groups[k]) > 0])} 个标签...")
        
        # 打乱最终的训练集和测试集
        print("打乱训练集和测试集...")
        random.shuffle(train_data)
        random.shuffle(test_data)
        
        # 保存结果
        print(f"保存训练集到：{train_output_file}")
        with open(train_output_file, 'w', encoding='utf-8') as f:
            json.dump(train_data, f, ensure_ascii=False, indent=2)
        
        print(f"保存测试集到：{test_output_file}")
        with open(test_output_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False, indent=2)
        
        # 显示统计信息
        print_statistics(split_stats)
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
        return False
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        return False

def print_statistics(stats):
    """打印统计信息"""
    print("\n" + "="*60)
    print("📊 划分统计信息")
    print("="*60)
    
    print(f"总标签数：{stats['total_labels']:,}")
    print(f"只有训练集的标签数：{stats['train_only_labels']:,}")
    print(f"既有训练集又有测试集的标签数：{stats['train_test_labels']:,}")
    print(f"训练集总条目数：{stats['total_train_items']:,}")
    print(f"测试集总条目数：{stats['total_test_items']:,}")
    
    total_items = stats['total_train_items'] + stats['total_test_items']
    if total_items > 0:
        train_ratio = stats['total_train_items'] / total_items * 100
        test_ratio = stats['total_test_items'] / total_items * 100
        print(f"训练集比例：{train_ratio:.1f}%")
        print(f"测试集比例：{test_ratio:.1f}%")
    
    print(f"\n📈 按题目数量分布：")
    print("-" * 40)
    for count in sorted(stats['distribution'].keys()):
        label_num = stats['distribution'][count]
        print(f"  {count:2d} 个题目的标签：{label_num:4d} 个")
    
    print("="*60)

def main():
    input_file = r"D:\python_project\dify\ultimate_merge_40.json"
    train_output_file = r"D:\python_project\dify\train_set.json"
    test_output_file = r"D:\python_project\dify\test_set.json"
    
    print("=" * 60)
    print("🚀 脚本4：按标签划分训练集和测试集")
    print("=" * 60)
    
    # 设置随机种子以便复现结果
    random.seed(42)
    
    # 执行划分
    success = process_train_test_split(input_file, train_output_file, test_output_file)
    
    if success:
        print("\n✅ 训练集和测试集划分完成！")
        print(f"📁 训练集文件：{train_output_file}")
        print(f"📁 测试集文件：{test_output_file}")
    else:
        print("\n❌ 训练集和测试集划分失败！")

if __name__ == "__main__":
    main()
