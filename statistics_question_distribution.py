#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计脚本：专门统计题目数量分布
"""

import json
import os
from collections import defaultdict, Counter
import matplotlib.pyplot as plt

def analyze_question_distribution(file_path):
    """
    分析文件中的题目数量分布
    
    Args:
        file_path (str): 文件路径
        
    Returns:
        dict: 统计结果
    """
    if not os.path.exists(file_path):
        print(f"错误：文件 {file_path} 不存在")
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"📁 分析文件：{os.path.basename(file_path)}")
        print(f"📊 总条目数：{len(data):,}")
        
        # 按标签分组统计
        label_groups = defaultdict(list)
        
        for item in data:
            if 'doc_label' in item:
                label_key = " -> ".join(item['doc_label'])
                label_groups[label_key].append(item)
        
        # 统计每个标签的题目数量
        question_counts = []
        for label_key, items in label_groups.items():
            question_counts.append(len(items))
        
        # 生成统计结果
        stats = {
            'total_labels': len(label_groups),
            'total_questions': len(data),
            'question_counts': question_counts,
            'distribution': Counter(question_counts),
            'min_questions': min(question_counts) if question_counts else 0,
            'max_questions': max(question_counts) if question_counts else 0,
            'avg_questions': sum(question_counts) / len(question_counts) if question_counts else 0
        }
        
        return stats
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
        return None
    except Exception as e:
        print(f"分析过程中出现错误：{e}")
        return None

def print_distribution_table(stats):
    """打印分布表格"""
    print(f"\n📈 题目数量分布统计：")
    print("=" * 70)
    print(f"{'题目数量':<8} {'标签数量':<10} {'占比':<8} {'累计题目':<12} {'累计占比':<8}")
    print("-" * 70)
    
    distribution = stats['distribution']
    total_labels = stats['total_labels']
    total_questions = stats['total_questions']
    
    cumulative_labels = 0
    cumulative_questions = 0
    
    for count in sorted(distribution.keys()):
        label_num = distribution[count]
        questions_in_this_group = count * label_num
        
        cumulative_labels += label_num
        cumulative_questions += questions_in_this_group
        
        label_ratio = label_num / total_labels * 100
        cumulative_ratio = cumulative_questions / total_questions * 100
        
        print(f"{count:<8} {label_num:<10} {label_ratio:<7.1f}% {cumulative_questions:<12,} {cumulative_ratio:<7.1f}%")

def print_summary_stats(stats):
    """打印汇总统计"""
    print(f"\n📊 汇总统计：")
    print("=" * 50)
    print(f"总标签数：{stats['total_labels']:,}")
    print(f"总题目数：{stats['total_questions']:,}")
    print(f"最少题目数：{stats['min_questions']}")
    print(f"最多题目数：{stats['max_questions']}")
    print(f"平均题目数：{stats['avg_questions']:.2f}")
    
    # 计算中位数
    sorted_counts = sorted(stats['question_counts'])
    n = len(sorted_counts)
    if n % 2 == 0:
        median = (sorted_counts[n//2-1] + sorted_counts[n//2]) / 2
    else:
        median = sorted_counts[n//2]
    print(f"中位数题目数：{median:.1f}")

def print_special_categories(stats):
    """打印特殊分类统计"""
    distribution = stats['distribution']
    total_labels = stats['total_labels']
    
    print(f"\n🎯 特殊分类统计：")
    print("=" * 50)
    
    # 少题目标签（≤3题）
    few_questions = sum(distribution[i] for i in range(1, 4) if i in distribution)
    few_ratio = few_questions / total_labels * 100
    print(f"少题目标签（≤3题）：{few_questions:,} 个 ({few_ratio:.1f}%)")
    
    # 中等题目标签（4-10题）
    medium_questions = sum(distribution[i] for i in range(4, 11) if i in distribution)
    medium_ratio = medium_questions / total_labels * 100
    print(f"中等题目标签（4-10题）：{medium_questions:,} 个 ({medium_ratio:.1f}%)")
    
    # 多题目标签（11-30题）
    many_questions = sum(distribution[i] for i in range(11, 31) if i in distribution)
    many_ratio = many_questions / total_labels * 100
    print(f"多题目标签（11-30题）：{many_questions:,} 个 ({many_ratio:.1f}%)")
    
    # 超多题目标签（>30题）
    very_many_questions = sum(distribution[i] for i in distribution.keys() if i > 30)
    very_many_ratio = very_many_questions / total_labels * 100
    print(f"超多题目标签（>30题）：{very_many_questions:,} 个 ({very_many_ratio:.1f}%)")

def print_top_categories(stats, top_n=10):
    """打印题目数量最多的分类"""
    distribution = stats['distribution']
    
    print(f"\n🏆 题目数量最多的 {top_n} 个分类：")
    print("=" * 40)
    
    sorted_items = sorted(distribution.items(), key=lambda x: x[0], reverse=True)
    
    for i, (count, label_num) in enumerate(sorted_items[:top_n]):
        total_questions_in_category = count * label_num
        print(f"{i+1:2d}. {count:2d}题/标签 × {label_num:3d}个标签 = {total_questions_in_category:5,}题")

def save_distribution_chart(stats, output_file="question_distribution.png"):
    """保存分布图表"""
    try:
        import matplotlib.pyplot as plt
        import matplotlib
        matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        matplotlib.rcParams['axes.unicode_minus'] = False
        
        distribution = stats['distribution']
        
        # 准备数据（只显示前20个最常见的题目数量）
        sorted_items = sorted(distribution.items())[:20]
        counts = [item[0] for item in sorted_items]
        frequencies = [item[1] for item in sorted_items]
        
        # 创建图表
        plt.figure(figsize=(12, 6))
        bars = plt.bar(counts, frequencies, alpha=0.7, color='skyblue', edgecolor='navy')
        
        # 添加数值标签
        for bar, freq in zip(bars, frequencies):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{freq}', ha='center', va='bottom', fontsize=8)
        
        plt.xlabel('每个标签的题目数量')
        plt.ylabel('标签数量')
        plt.title('题目数量分布图')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"\n📈 分布图表已保存到：{output_file}")
        
    except ImportError:
        print("\n⚠️  matplotlib 未安装，跳过图表生成")
    except Exception as e:
        print(f"\n⚠️  生成图表时出现错误：{e}")

def main():
    # 要分析的文件列表
    files_to_analyze = [
        r"D:\python_project\dify\ultimate_merge_40.json",
        r"D:\python_project\dify\train_set.json",
        r"D:\python_project\dify\test_set.json"
    ]
    
    print("=" * 80)
    print("📊 题目数量分布统计分析")
    print("=" * 80)
    
    for file_path in files_to_analyze:
        if os.path.exists(file_path):
            print(f"\n{'='*60}")
            stats = analyze_question_distribution(file_path)
            
            if stats:
                print_summary_stats(stats)
                print_special_categories(stats)
                print_top_categories(stats)
                print_distribution_table(stats)
                
                # 为原始文件生成图表
                if "ultimate_merge_40.json" in file_path:
                    save_distribution_chart(stats, "ultimate_merge_40_distribution.png")
            
            print(f"{'='*60}")
        else:
            print(f"\n❌ 文件不存在：{os.path.basename(file_path)}")
    
    print(f"\n✅ 统计分析完成！")

if __name__ == "__main__":
    main()
