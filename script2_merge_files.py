#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本2：验证子集关系并合并文件
"""

import json
import os

def verify_subset_and_merge(processed_file, ultimate_file, output_file):
    """
    验证 processed_labels_output.json 是否为 ultimate_merge_small_labels_numbered.json 的子集
    如果是，则进行合并
    
    Args:
        processed_file (str): processed_labels_output.json 文件路径
        ultimate_file (str): ultimate_merge_small_labels_numbered.json 文件路径
        output_file (str): 输出文件路径
    """
    # 检查文件是否存在
    if not os.path.exists(processed_file):
        print(f"错误：文件 {processed_file} 不存在")
        return False
    
    if not os.path.exists(ultimate_file):
        print(f"错误：文件 {ultimate_file} 不存在")
        return False
    
    try:
        # 读取两个文件
        with open(processed_file, 'r', encoding='utf-8') as f:
            processed_data = json.load(f)
        
        with open(ultimate_file, 'r', encoding='utf-8') as f:
            ultimate_data = json.load(f)
        
        print(f"读取文件完成：")
        print(f"  - processed_labels_output.json: {len(processed_data)} 个条目")
        print(f"  - ultimate_merge_small_labels_numbered.json: {len(ultimate_data)} 个条目")
        
        # 提取 label_path 集合
        processed_labels = set()
        processed_dict = {}
        
        for item in processed_data:
            if 'label_path' in item:
                label_path = item['label_path']
                processed_labels.add(label_path)
                processed_dict[label_path] = item
        
        ultimate_labels = set()
        ultimate_dict = {}
        
        for item in ultimate_data:
            if 'label_path' in item:
                label_path = item['label_path']
                ultimate_labels.add(label_path)
                ultimate_dict[label_path] = item
        
        print(f"\n标签路径统计：")
        print(f"  - processed 文件中的唯一标签路径: {len(processed_labels)}")
        print(f"  - ultimate 文件中的唯一标签路径: {len(ultimate_labels)}")
        
        # 验证子集关系
        print("\n验证子集关系...")
        if not processed_labels.issubset(ultimate_labels):
            missing_labels = processed_labels - ultimate_labels
            print(f"❌ processed_labels_output.json 不是 ultimate_merge_small_labels_numbered.json 的子集")
            print(f"缺失的标签路径有 {len(missing_labels)} 个：")
            for i, label in enumerate(sorted(missing_labels)[:10]):  # 只显示前10个
                print(f"  {i+1}. {label}")
            if len(missing_labels) > 10:
                print(f"  ... 还有 {len(missing_labels) - 10} 个")
            return False
        
        print("✅ 验证通过：processed_labels_output.json 是 ultimate_merge_small_labels_numbered.json 的子集")
        
        # 找到共同的标签路径
        common_labels = processed_labels & ultimate_labels
        print(f"共同的标签路径有 {len(common_labels)} 个")
        
        # 开始合并
        print("\n开始合并文件...")
        merged_data = []
        
        for item in ultimate_data:
            label_path = item.get('label_path', '')
            
            if label_path in common_labels:
                # 使用 processed 文件中的 example_questions
                merged_item = item.copy()
                merged_item['example_questions'] = processed_dict[label_path]['example_questions']
                merged_data.append(merged_item)
                print(f"  替换标签: {label_path[:50]}..." if len(label_path) > 50 else f"  替换标签: {label_path}")
            else:
                # 保持 ultimate 文件中的原始内容
                merged_data.append(item)
        
        # 保存合并结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(merged_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n合并完成！")
        print(f"  - 总条目数: {len(merged_data)}")
        print(f"  - 替换的条目数: {len(common_labels)}")
        print(f"  - 保持原样的条目数: {len(merged_data) - len(common_labels)}")
        print(f"  - 结果已保存到: {output_file}")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"JSON解析错误：{e}")
        return False
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        return False

def main():
    processed_file = r"D:\python_project\dify\processed_labels_output.json"
    ultimate_file = r"D:\python_project\dify\ultimate_merge_small_labels_numbered.json"
    output_file = r"D:\python_project\dify\merged_result.json"
    
    print("=" * 60)
    print("脚本3：验证子集关系并合并文件")
    print("=" * 60)
    
    # 执行验证和合并
    success = verify_subset_and_merge(processed_file, ultimate_file, output_file)
    
    if success:
        print("\n✅ 验证和合并完成！")
    else:
        print("\n❌ 验证或合并失败！")

if __name__ == "__main__":
    main()
