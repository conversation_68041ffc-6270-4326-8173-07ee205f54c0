import os
import json
import requests
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from tqdm import tqdm

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("label_processing.log"),
        logging.StreamHandler()
    ]
)

class DifyLabelProcessor:
    def __init__(self, dify_api_key: str, workflow_id: str,
                 input_json_path: str = r"D:\python_project\dify\ultimate_merge_small_labels_numbered.json",
                 output_json_path: str = "processed_labels_output.json",
                 dify_base_url: str = "https://dify.xmdas-link.com",
                 target_questions_count: int = 40,
                 max_concurrent_requests: int = 5):
        """
        初始化Dify标签处理器
        """
        self.dify_api_key = dify_api_key
        self.workflow_id = workflow_id
        self.input_json_path = input_json_path
        self.output_json_path = output_json_path
        self.dify_base_url = dify_base_url
        self.target_questions_count = target_questions_count
        self.max_concurrent_requests = max_concurrent_requests

        # 设置API地址
        self.dify_workflow_url = f"{self.dify_base_url}/v1/workflows/run"
        self.headers = {
            "Authorization": f"Bearer {self.dify_api_key}",
            "Content-Type": "application/json"
        }

        # 创建优化的HTTP会话
        self.session = self.create_optimized_session()

        # 显示连接池配置
        estimated_max_connections = self.max_concurrent_requests * 10
        pool_size = max(100, estimated_max_connections * 2)
        print(f"🔧 连接池配置: 数据项并发={self.max_concurrent_requests}, 预估最大连接数={estimated_max_connections}, 连接池大小={pool_size}")

        # 存储处理结果
        self.processed_results = []

        # 加载已存在的结果（支持断点续传）
        self.load_existing_results()

    def create_optimized_session(self) -> requests.Session:
        """创建优化的HTTP会话，支持连接池和重试"""
        session = requests.Session()
        
        # 重试策略
        retry_strategy = Retry(
            total=2,  # 减少重试次数
            backoff_factor=0.3,
            status_forcelist=[429, 500, 502, 503, 504],
        )

        # 动态计算连接池大小
        # 实际最大并发 = 数据项并发数 × 10 (每个数据项的API并发数)
        estimated_max_connections = self.max_concurrent_requests * 10
        pool_size = max(100, estimated_max_connections * 2)  # 2倍缓冲确保足够

        # 连接池配置
        adapter = HTTPAdapter(
            pool_connections=pool_size,
            pool_maxsize=pool_size,
            max_retries=retry_strategy
        )
        
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session

    def load_existing_results(self) -> None:
        """从输出文件加载已存在的结果，支持断点续传"""
        try:
            if os.path.exists(self.output_json_path):
                with open(self.output_json_path, 'r', encoding='utf-8') as f:
                    self.processed_results = json.load(f)
                    print(f"已加载 {len(self.processed_results)} 个已存在的结果")
        except Exception as e:
            print(f"加载已存在结果时发生错误: {str(e)}")
            self.processed_results = []

    def load_input_data(self) -> List[Dict]:
        """加载输入JSON文件"""
        try:
            with open(self.input_json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                print(f"成功加载 {len(data)} 条输入数据")
                return data
        except Exception as e:
            logging.error(f"加载输入文件失败: {str(e)}")
            return []

    def run_dify_workflow(self, label_path: str, example_questions: List[str]) -> Optional[str]:
        """调用Dify工作流生成单个问题"""
        try:
            # 构建payload，根据你的Dify工作流输入字段配置
            # 将example_questions转换为字符串格式，因为Dify可能需要字符串输入
            example_questions_str = "\n".join([f"{i+1}、{q}" for i, q in enumerate(example_questions)])

            payload = {
                "inputs": {
                    "label_path": label_path,
                    "example_questions": example_questions_str
                },
                "response_mode": "blocking",
                "user": "label_processor_script",
                "workflow": self.workflow_id
            }

            response = self.session.post(
                self.dify_workflow_url,
                headers=self.headers,
                json=payload,
                timeout=120  # 增加超时时间
            )

            # 检查响应内容是否为空
            if not response.text.strip():
                logging.warning("Dify API返回空响应")
                return None

            response.raise_for_status()

            try:
                result = response.json()
            except json.JSONDecodeError as e:
                logging.error(f"解析JSON响应失败: {str(e)}")
                return None

            # 提取输出结果
            if "data" in result and "outputs" in result["data"]:
                outputs = result["data"]["outputs"]

                # 检查outputs是否为空
                if not outputs:
                    logging.debug("outputs字段为空，可能是API限流或临时错误")
                    return None

                if "result" in outputs:
                    return str(outputs["result"])
                elif "output" in outputs:
                    return str(outputs["output"])
                else:
                    return str(outputs)
            elif "output" in result:
                return str(result["output"])
            else:
                logging.warning(f"未找到预期的输出格式，完整响应: {result}")
                return None

        except requests.exceptions.Timeout:
            logging.error("请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logging.error(f"请求失败: {str(e)}")
            return None
        except Exception as e:
            logging.error(f"调用Dify工作流时发生未知错误: {str(e)}")
            return None



    def parse_dify_output(self, output: str) -> Optional[str]:
        """解析Dify输出，提取answer字段中的问题"""
        try:
            # 预处理输出，修复常见的JSON问题
            cleaned_output = self.clean_json_output(output)

            # 尝试直接解析为JSON
            try:
                parsed_data = json.loads(cleaned_output)

                if isinstance(parsed_data, dict) and "answer" in parsed_data:
                    answer_list = parsed_data["answer"]
                    if isinstance(answer_list, list) and len(answer_list) > 0:
                        # 返回第一个answer元素
                        return str(answer_list[0])
                    elif isinstance(answer_list, str):
                        return answer_list
                else:
                    logging.debug(f"JSON中没有找到'answer'字段，可用字段: {list(parsed_data.keys()) if isinstance(parsed_data, dict) else 'N/A'}")
                return None
            except json.JSONDecodeError as e:
                logging.debug(f"JSON解析失败: {str(e)}")
                # 如果不是JSON，尝试提取JSON部分
                extracted_answer = self.extract_answer_from_text(output)
                if extracted_answer:
                    return extracted_answer
                return None

        except Exception as e:
            logging.error(f"解析Dify输出失败: {str(e)}")
            return None

    def clean_json_output(self, output: str) -> str:
        """清理JSON输出，修复常见的格式问题"""
        try:
            # 修复常见的转义字符问题
            # 将单个反斜杠替换为双反斜杠（但要小心不要影响已经正确转义的）
            import re

            # 修复未转义的反斜杠（但保留已经正确转义的）
            # 这个正则表达式匹配单个反斜杠，但不匹配已经转义的双反斜杠
            output = re.sub(r'(?<!\\)\\(?!["\\\/bfnrt])', r'\\\\', output)

            return output
        except Exception as e:
            logging.warning(f"清理JSON输出时发生错误: {str(e)}")
            return output

    def extract_answer_from_text(self, text: str) -> Optional[str]:
        """从文本中提取answer字段的内容"""
        try:
            # 查找JSON代码块
            if "```json" in text:
                start_marker = "```json"
                end_marker = "```"
                start_idx = text.find(start_marker)
                if start_idx != -1:
                    start_idx += len(start_marker)
                    end_idx = text.find(end_marker, start_idx)
                    if end_idx != -1:
                        json_content = text[start_idx:end_idx].strip()
                        try:
                            parsed_data = json.loads(json_content)
                            if isinstance(parsed_data, dict) and "answer" in parsed_data:
                                answer_list = parsed_data["answer"]
                                if isinstance(answer_list, list) and len(answer_list) > 0:
                                    return str(answer_list[0])
                                elif isinstance(answer_list, str):
                                    return answer_list
                        except json.JSONDecodeError:
                            pass

            # 查找包含answer的部分
            if '"answer":' in text:
                try:
                    # 尝试找到完整的JSON对象
                    start_idx = text.find('{')
                    if start_idx != -1:
                        # 简单的括号匹配
                        bracket_count = 0
                        end_idx = start_idx
                        for i, char in enumerate(text[start_idx:], start_idx):
                            if char == '{':
                                bracket_count += 1
                            elif char == '}':
                                bracket_count -= 1
                                if bracket_count == 0:
                                    end_idx = i + 1
                                    break

                        if bracket_count == 0:
                            json_content = text[start_idx:end_idx]
                            parsed_data = json.loads(json_content)
                            if isinstance(parsed_data, dict) and "answer" in parsed_data:
                                answer_list = parsed_data["answer"]
                                if isinstance(answer_list, list) and len(answer_list) > 0:
                                    return str(answer_list[0])
                                elif isinstance(answer_list, str):
                                    return answer_list
                except (json.JSONDecodeError, ValueError):
                    pass

            return None

        except Exception as e:
            logging.warning(f"Answer提取失败: {str(e)}")
            return None

    def generate_questions_for_item(self, item: Dict) -> Optional[Dict]:
        """为单个数据项生成所需数量的问题"""
        try:
            label_path = item.get("label_path", "")
            existing_questions = item.get("example_questions", [])

            if not label_path:
                logging.warning("数据项缺少label_path")
                return None

            current_count = len(existing_questions)
            needed_count = max(0, self.target_questions_count - current_count)

            if needed_count == 0:
                logging.info(f"标签 {label_path[:50]}... 已有足够问题({current_count}个)，跳过")
                return {
                    "label_path": label_path,
                    "example_questions": existing_questions
                }

            logging.info(f"标签 {label_path[:50]}... 需要生成 {needed_count} 个问题")

            # 并行生成问题
            new_questions = self.generate_questions_parallel(label_path, existing_questions, needed_count)

            # 合并原有问题和新生成的问题
            all_questions = existing_questions + new_questions

            return {
                "label_path": label_path,
                "example_questions": all_questions
            }

        except Exception as e:
            logging.error(f"生成问题时发生错误: {str(e)}")
            return None

    def generate_questions_parallel(self, label_path: str, existing_questions: List[str], needed_count: int) -> List[str]:
        """并行生成指定数量的问题 - 使用批次处理避免API限流"""
        new_questions = []
        max_retries = 2  # 增加重试次数

        # 使用更保守的API并发数，避免触发限流
        api_concurrent_limit = min(3, needed_count)  # 降低并发数到3
        batch_size = api_concurrent_limit  # 每批处理的数量

        logging.info(f"使用批次处理: 每批{batch_size}个请求，总共需要{needed_count}个问题")

        # 分批处理，避免同时发起太多请求
        for batch_start in range(0, needed_count, batch_size):
            batch_end = min(batch_start + batch_size, needed_count)
            batch_needed = batch_end - batch_start

            logging.info(f"处理第{batch_start//batch_size + 1}批: {batch_needed}个请求")

            # 使用线程池并行处理当前批次的API调用
            with ThreadPoolExecutor(max_workers=batch_needed) as executor:
                # 提交当前批次的任务
                future_to_index = {
                    executor.submit(self.run_dify_workflow_with_retry, label_path, existing_questions, max_retries): i
                    for i in range(batch_needed)
                }

                # 收集当前批次的结果
                batch_questions = []
                for future in as_completed(future_to_index):
                    try:
                        result = future.result()
                        if result:
                            parsed_question = self.parse_dify_output(result)
                            if parsed_question:
                                batch_questions.append(parsed_question)
                            else:
                                logging.debug(f"解析Dify输出失败，跳过该问题")
                        else:
                            logging.debug(f"Dify API调用失败，跳过该问题")
                    except Exception as e:
                        logging.error(f"处理并行任务时发生错误: {str(e)}")

                new_questions.extend(batch_questions)
                logging.info(f"第{batch_start//batch_size + 1}批完成: 成功生成{len(batch_questions)}个问题")

            # 批次间添加延迟，避免API限流
            if batch_end < needed_count:  # 不是最后一批
                time.sleep(2)  # 批次间等待2秒
                logging.info(f"批次间等待2秒...")

        logging.info(f"所有批次完成: 成功生成 {len(new_questions)} 个问题（目标: {needed_count}）")
        return new_questions

    def run_dify_workflow_with_retry(self, label_path: str, example_questions: List[str], max_retries: int = 2) -> Optional[str]:
        """带重试机制的Dify工作流调用"""
        for attempt in range(max_retries + 1):
            try:
                # 在每次API调用前添加小延迟，避免请求过于密集
                if attempt > 0:  # 重试时等待更长时间
                    time.sleep(1 + attempt * 0.5)  # 递增延迟：1s, 1.5s, 2s...
                else:
                    time.sleep(0.2)  # 首次调用前短暂延迟

                result = self.run_dify_workflow(label_path, example_questions)
                if result:  # 如果成功获得结果，直接返回
                    return result
                else:
                    if attempt < max_retries:
                        logging.debug(f"第 {attempt + 1} 次尝试失败，等待后重试...")
                    else:
                        logging.debug(f"经过 {max_retries + 1} 次尝试后仍然失败")
            except Exception as e:
                if attempt < max_retries:
                    logging.debug(f"第 {attempt + 1} 次尝试出现异常: {str(e)}，等待后重试...")
                else:
                    logging.debug(f"经过 {max_retries + 1} 次尝试后仍然出现异常: {str(e)}")

        return None





    def process_single_item(self, item: Dict, index: int) -> Optional[Dict]:
        """处理单个数据项"""
        try:
            label_path = item.get("label_path", "")

            if not label_path:
                logging.warning(f"第 {index + 1} 项缺少label_path")
                return None

            print(f"正在处理第 {index + 1} 项: {label_path[:50]}...")

            # 生成问题
            result = self.generate_questions_for_item(item)

            if result is None:
                logging.error(f"第 {index + 1} 项处理失败")
                return None

            return result

        except Exception as e:
            logging.error(f"处理第 {index + 1} 项时发生错误: {str(e)}")
            return None

    def save_results(self) -> None:
        """保存处理结果到JSON文件"""
        try:
            with open(self.output_json_path, 'w', encoding='utf-8') as f:
                json.dump(self.processed_results, f, ensure_ascii=False, indent=2)
            
            file_size = os.path.getsize(self.output_json_path) / 1024  # KB
            print(f"✅ 结果已保存到: {self.output_json_path} ({len(self.processed_results)}个结果, {file_size:.1f}KB)")
            
        except Exception as e:
            logging.error(f"保存结果失败: {str(e)}")

    def process_all_items(self) -> None:
        """处理所有数据项 - 使用数据项级别的并发处理"""
        # 加载输入数据
        input_data = self.load_input_data()
        if not input_data:
            print("没有找到可处理的数据")
            return

        # 获取已处理的label_path，用于断点续传
        processed_label_paths = {item.get("label_path", "") for item in self.processed_results}

        # 过滤出未处理的数据
        remaining_data = [item for item in input_data
                         if item.get("label_path", "") not in processed_label_paths]

        if not remaining_data:
            print("所有数据都已处理完成！")
            return

        print(f"开始处理 {len(remaining_data)} 个未处理的数据项...")
        print(f"目标问题数量: {self.target_questions_count}")
        print(f"数据项级别并发数: {self.max_concurrent_requests}")

        # 使用数据项级别的并发处理
        self.process_items_concurrently(remaining_data)

    def process_items_concurrently(self, items_to_process: List[Dict]) -> None:
        """并发处理多个数据项"""
        success_count = 0
        failed_count = 0

        # 创建进度条
        pbar = tqdm(total=len(items_to_process), desc="处理进度")

        # 使用线程池并发处理数据项
        with ThreadPoolExecutor(max_workers=self.max_concurrent_requests) as executor:
            # 提交所有任务
            future_to_item = {
                executor.submit(self.generate_questions_for_item, item): (index, item)
                for index, item in enumerate(items_to_process)
            }

            # 收集结果
            for future in as_completed(future_to_item):
                index, original_item = future_to_item[future]
                try:
                    result = future.result()
                    if result:
                        self.processed_results.append(result)
                        success_count += 1

                        # 定期保存结果（每10个成功结果保存一次）
                        if success_count % 10 == 0:
                            self.save_results()

                    else:
                        failed_count += 1
                        logging.error(f"处理数据项失败: {original_item.get('label_path', '')[:50]}...")

                except Exception as e:
                    failed_count += 1
                    logging.error(f"处理数据项时发生异常: {str(e)}")

                # 更新进度条
                pbar.update(1)

        pbar.close()

        # 最终保存结果
        self.save_results()

        print(f"\n📈 处理完成统计:")
        print(f"   总数量: {len(items_to_process)}")
        print(f"   成功: {success_count}")
        print(f"   失败: {failed_count}")
        print(f"   总结果数: {len(self.processed_results)}")

    def test_single_item(self, index: int = 0) -> None:
        """测试处理单个数据项"""
        input_data = self.load_input_data()
        if not input_data or index >= len(input_data):
            print(f"无法找到索引为 {index} 的数据项")
            return

        item = input_data[index]
        existing_count = len(item.get('example_questions', []))
        needed_count = max(0, self.target_questions_count - existing_count)

        print(f"测试处理第 {index + 1} 项:")
        print(f"Label Path: {item.get('label_path', '')}")
        print(f"现有问题数量: {existing_count}")
        print(f"需要生成问题数量: {needed_count}")
        print(f"目标总问题数量: {self.target_questions_count}")
        print("\n开始调用Dify API...")

        result = self.process_single_item(item, index)

        if result:
            print("\n✅ 处理成功！")
            print(f"输出的Label Path: {result['label_path']}")
            print(f"输出的Example Questions数量: {len(result['example_questions'])}")
            print("\n输出的Example Questions（显示最后3个新生成的）:")
            questions = result['example_questions']
            start_idx = max(0, existing_count)
            for i, q in enumerate(questions[start_idx:start_idx+3]):
                print(f"  {start_idx+i+1}. {q[:100]}...")

            # 保存测试结果
            test_output_path = "test_result.json"
            with open(test_output_path, 'w', encoding='utf-8') as f:
                json.dump([result], f, ensure_ascii=False, indent=2)
            print(f"\n测试结果已保存到: {test_output_path}")
        else:
            print("\n❌ 处理失败！")


if __name__ == "__main__":
    import sys

    # 从get_analysis.py中获取的配置信息
    DIFY_API_KEY = "app-vFRSxQl0lfmPukuyeyQjWeZg"
    WORKFLOW_ID = "4KdwtghEk8bN2yUz"  # 请确认这是正确的workflow_id
    DIFY_BASE_URL = "https://dify.xmdas-link.com"

    processor = DifyLabelProcessor(
        dify_api_key=DIFY_API_KEY,
        workflow_id=WORKFLOW_ID,
        input_json_path="/data/zhousiqi/ultimate_merge_small_labels_numbered.json",
        output_json_path="processed_labels_output.json",
        dify_base_url=DIFY_BASE_URL,
        target_questions_count=40,  # 目标问题数量
        max_concurrent_requests=5   # 最大并发请求数
    )

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == "--test":
        # 测试模式
        test_index = 0
        if len(sys.argv) > 2:
            try:
                test_index = int(sys.argv[2])
            except ValueError:
                print("测试索引必须是数字")
                sys.exit(1)

        print(f"🧪 启动测试模式，测试第 {test_index + 1} 项数据")
        processor.test_single_item(test_index)
    else:
        # 完整处理模式
        print("🚀 启动完整处理模式")
        print("💡 提示: 使用 --test [索引] 参数可以测试单个数据项")
        print("   例如: python process_labels_with_dify.py --test 0")
        processor.process_all_items()
